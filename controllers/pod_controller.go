package controllers

import (
	"context"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/predicate"

	"github.com/couchbase/monitoring-operator/pkg/config"
)

// PodReconciler reconciles Couchbase pods and creates companion NGINX pods
type PodReconciler struct {
	client.Client
	Scheme *runtime.Scheme
	Config *config.Config
}

//+kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups="",resources=pods/status,verbs=get;update;patch
//+kubebuilder:rbac:groups="",resources=pods/finalizers,verbs=update

// Reconcile handles pod events and manages companion NGINX pods
func (r *PodReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Fetch the Couchbase pod
	var couchbasePod corev1.Pod
	if err := r.Get(ctx, req.NamespacedName, &couchbasePod); err != nil {
		if errors.IsNotFound(err) {
			// Pod was deleted, clean up companion NGINX pod
			logger.Info("Couchbase pod deleted, cleaning up companion NGINX pod", "pod", req.NamespacedName)
			return r.cleanupNginxPod(ctx, req.NamespacedName)
		}
		logger.Error(err, "unable to fetch Couchbase pod")
		return ctrl.Result{}, err
	}

	// Check if this is a Couchbase pod we should monitor
	if !r.isCouchbasePod(&couchbasePod) {
		return ctrl.Result{}, nil
	}

	// Check if pod is being deleted
	if couchbasePod.DeletionTimestamp != nil {
		logger.Info("Couchbase pod is being deleted, cleaning up companion NGINX pod", "pod", req.NamespacedName)
		return r.cleanupNginxPod(ctx, req.NamespacedName)
	}

	// Only create NGINX pod when Couchbase pod is running
	if couchbasePod.Status.Phase != corev1.PodRunning {
		logger.Info("Couchbase pod is not running yet, skipping NGINX pod creation", 
			"pod", req.NamespacedName, "phase", couchbasePod.Status.Phase)
		return ctrl.Result{}, nil
	}

	// Create or update companion NGINX pod
	return r.reconcileNginxPod(ctx, &couchbasePod)
}

// isCouchbasePod checks if the pod matches our Couchbase selector
func (r *PodReconciler) isCouchbasePod(pod *corev1.Pod) bool {
	// Parse the selector (simple key=value format for now)
	selectorParts := strings.SplitN(r.Config.CouchbasePodSelector, "=", 2)
	if len(selectorParts) != 2 {
		return false
	}
	
	key := strings.TrimSpace(selectorParts[0])
	value := strings.TrimSpace(selectorParts[1])
	
	if podValue, exists := pod.Labels[key]; exists && podValue == value {
		return true
	}
	
	return false
}

// reconcileNginxPod creates or updates the companion NGINX pod
func (r *PodReconciler) reconcileNginxPod(ctx context.Context, couchbasePod *corev1.Pod) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	
	nginxPodName := r.generateNginxPodName(couchbasePod.Name)
	nginxPod := &corev1.Pod{}
	
	err := r.Get(ctx, types.NamespacedName{
		Name:      nginxPodName,
		Namespace: couchbasePod.Namespace,
	}, nginxPod)
	
	if err != nil && errors.IsNotFound(err) {
		// Create new NGINX pod
		nginxPod = r.buildNginxPod(couchbasePod, nginxPodName)
		
		logger.Info("Creating companion NGINX pod", 
			"couchbase-pod", couchbasePod.Name, 
			"nginx-pod", nginxPodName)
		
		if err := r.Create(ctx, nginxPod); err != nil {
			logger.Error(err, "unable to create NGINX pod", "pod", nginxPodName)
			return ctrl.Result{}, err
		}
		
		return ctrl.Result{}, nil
	} else if err != nil {
		logger.Error(err, "unable to fetch NGINX pod")
		return ctrl.Result{}, err
	}
	
	// NGINX pod already exists, check if it needs updates
	logger.Info("Companion NGINX pod already exists", 
		"couchbase-pod", couchbasePod.Name, 
		"nginx-pod", nginxPodName)
	
	return ctrl.Result{}, nil
}

// cleanupNginxPod removes the companion NGINX pod when Couchbase pod is deleted
func (r *PodReconciler) cleanupNginxPod(ctx context.Context, couchbasePodName types.NamespacedName) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	
	nginxPodName := r.generateNginxPodName(couchbasePodName.Name)
	nginxPod := &corev1.Pod{}
	
	err := r.Get(ctx, types.NamespacedName{
		Name:      nginxPodName,
		Namespace: couchbasePodName.Namespace,
	}, nginxPod)
	
	if err != nil && errors.IsNotFound(err) {
		// NGINX pod doesn't exist, nothing to clean up
		return ctrl.Result{}, nil
	} else if err != nil {
		logger.Error(err, "unable to fetch NGINX pod for cleanup")
		return ctrl.Result{}, err
	}
	
	// Delete the NGINX pod
	logger.Info("Deleting companion NGINX pod", "nginx-pod", nginxPodName)
	if err := r.Delete(ctx, nginxPod); err != nil {
		logger.Error(err, "unable to delete NGINX pod", "pod", nginxPodName)
		return ctrl.Result{}, err
	}
	
	return ctrl.Result{}, nil
}

// generateNginxPodName creates a name for the companion NGINX pod
func (r *PodReconciler) generateNginxPodName(couchbasePodName string) string {
	return fmt.Sprintf("%s-%s", r.Config.NginxPodPrefix, couchbasePodName)
}
