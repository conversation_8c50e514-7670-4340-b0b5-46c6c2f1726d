package controllers

import (
	"context"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/log"

	monitoringv1 "github.com/couchbase/monitoring-operator/api/v1"
)

// CompanionPodTemplateReconciler reconciles a CompanionPodTemplate object
type CompanionPodTemplateReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

//+kubebuilder:rbac:groups=monitoring.couchbase.com,resources=companionpodtemplates,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=monitoring.couchbase.com,resources=companionpodtemplates/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=monitoring.couchbase.com,resources=companionpodtemplates/finalizers,verbs=update
//+kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch;create;update;patch;delete

// Reconcile is part of the main kubernetes reconciliation loop
func (r *CompanionPodTemplateReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Fetch the CompanionPodTemplate instance
	var template monitoringv1.CompanionPodTemplate
	if err := r.Get(ctx, req.NamespacedName, &template); err != nil {
		if errors.IsNotFound(err) {
			logger.Info("CompanionPodTemplate resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		logger.Error(err, "Failed to get CompanionPodTemplate")
		return ctrl.Result{}, err
	}

	// Handle deletion
	if template.DeletionTimestamp != nil {
		return r.handleDeletion(ctx, &template)
	}

	// Add finalizer if not present
	if !controllerutil.ContainsFinalizer(&template, "monitoring.couchbase.com/finalizer") {
		controllerutil.AddFinalizer(&template, "monitoring.couchbase.com/finalizer")
		return ctrl.Result{}, r.Update(ctx, &template)
	}

	// Reconcile companion pods
	return r.reconcileCompanionPods(ctx, &template)
}

// reconcileCompanionPods handles the main reconciliation logic
func (r *CompanionPodTemplateReconciler) reconcileCompanionPods(ctx context.Context, template *monitoringv1.CompanionPodTemplate) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Find all pods that match the selector
	selector, err := metav1.LabelSelectorAsSelector(&template.Spec.Selector)
	if err != nil {
		logger.Error(err, "Failed to convert label selector")
		return ctrl.Result{}, err
	}

	var sourcePods corev1.PodList
	if err := r.List(ctx, &sourcePods, &client.ListOptions{
		Namespace:     template.Namespace,
		LabelSelector: selector,
	}); err != nil {
		logger.Error(err, "Failed to list source pods")
		return ctrl.Result{}, err
	}

	// Track statistics
	var matchedPods, companionPods int32

	// Process each source pod
	for _, sourcePod := range sourcePods.Items {
		matchedPods++

		// Skip if source pod is being deleted
		if sourcePod.DeletionTimestamp != nil {
			continue
		}

		// Check if we should create companion pod based on lifecycle policy
		if template.Spec.Lifecycle.CreateWhenReady && sourcePod.Status.Phase != corev1.PodRunning {
			logger.Info("Source pod not ready, skipping companion creation",
				"sourcePod", sourcePod.Name, "phase", sourcePod.Status.Phase)
			continue
		}

		// Generate companion pod name
		companionName := r.generateCompanionPodName(&sourcePod, &template.Spec.NamingStrategy)

		// Check if companion pod already exists
		var companionPod corev1.Pod
		err := r.Get(ctx, types.NamespacedName{
			Name:      companionName,
			Namespace: sourcePod.Namespace,
		}, &companionPod)

		if err != nil && errors.IsNotFound(err) {
			// Create companion pod
			companionPod = r.buildCompanionPod(&sourcePod, template, companionName)

			// Set owner reference
			if err := controllerutil.SetControllerReference(&sourcePod, &companionPod, r.Scheme); err != nil {
				logger.Error(err, "Failed to set owner reference", "companionPod", companionName)
				continue
			}

			logger.Info("Creating companion pod", "sourcePod", sourcePod.Name, "companionPod", companionName)
			if err := r.Create(ctx, &companionPod); err != nil {
				logger.Error(err, "Failed to create companion pod", "companionPod", companionName)
				continue
			}
			companionPods++
		} else if err != nil {
			logger.Error(err, "Failed to get companion pod", "companionPod", companionName)
			continue
		} else {
			// Companion pod exists
			companionPods++
		}
	}

	// Update status
	template.Status.MatchedPods = matchedPods
	template.Status.CompanionPods = companionPods
	now := metav1.NewTime(time.Now())
	template.Status.LastReconcileTime = &now

	// Update conditions
	r.updateConditions(&template.Status, matchedPods, companionPods)

	if err := r.Status().Update(ctx, template); err != nil {
		logger.Error(err, "Failed to update CompanionPodTemplate status")
		return ctrl.Result{}, err
	}

	logger.Info("Reconciliation completed",
		"template", template.Name,
		"matchedPods", matchedPods,
		"companionPods", companionPods)

	// Requeue after 30 seconds to check for new pods
	return ctrl.Result{RequeueAfter: time.Second * 30}, nil
}

// handleDeletion handles cleanup when CompanionPodTemplate is being deleted
func (r *CompanionPodTemplateReconciler) handleDeletion(ctx context.Context, template *monitoringv1.CompanionPodTemplate) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Find all companion pods created by this template
	// They should be cleaned up automatically via owner references
	logger.Info("CompanionPodTemplate is being deleted, companion pods will be cleaned up via owner references",
		"template", template.Name)

	// Remove finalizer
	controllerutil.RemoveFinalizer(template, "monitoring.couchbase.com/finalizer")
	return ctrl.Result{}, r.Update(ctx, template)
}

// generateCompanionPodName generates a name for the companion pod based on the naming strategy
func (r *CompanionPodTemplateReconciler) generateCompanionPodName(sourcePod *corev1.Pod, strategy *monitoringv1.NamingStrategy) string {
	switch strategy.Strategy {
	case "suffix":
		return fmt.Sprintf("%s-%s", sourcePod.Name, strategy.Suffix)
	case "template":
		return strings.ReplaceAll(strategy.Template, "{source-name}", sourcePod.Name)
	default: // "prefix"
		return fmt.Sprintf("%s-%s", strategy.Prefix, sourcePod.Name)
	}
}

// buildCompanionPod creates a companion pod based on the template and source pod
func (r *CompanionPodTemplateReconciler) buildCompanionPod(sourcePod *corev1.Pod, template *monitoringv1.CompanionPodTemplate, companionName string) corev1.Pod {
	// Start with the template
	companionPod := corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:        companionName,
			Namespace:   sourcePod.Namespace,
			Labels:      make(map[string]string),
			Annotations: make(map[string]string),
		},
		Spec: template.Spec.Template.Spec,
	}

	// Copy labels from source pod
	r.copyLabels(sourcePod, &companionPod, &template.Spec.LabelCopyPolicy)

	// Copy annotations from source pod
	r.copyAnnotations(sourcePod, &companionPod, &template.Spec.AnnotationCopyPolicy)

	// Apply template labels and annotations
	for k, v := range template.Spec.Template.Labels {
		companionPod.Labels[k] = v
	}
	for k, v := range template.Spec.Template.Annotations {
		companionPod.Annotations[k] = v
	}

	return companionPod
}

// copyLabels copies labels from source pod to companion pod based on policy
func (r *CompanionPodTemplateReconciler) copyLabels(sourcePod, companionPod *corev1.Pod, policy *monitoringv1.LabelCopyPolicy) {
	if policy.CopyAll {
		// Copy all labels except excluded ones
		for k, v := range sourcePod.Labels {
			if !r.isExcluded(k, policy.Exclude) {
				companionPod.Labels[k] = v
			}
		}
	} else {
		// Copy only included labels
		for _, key := range policy.Include {
			if value, exists := sourcePod.Labels[key]; exists {
				companionPod.Labels[key] = value
			}
		}
	}

	// Add additional labels
	for k, v := range policy.AdditionalLabels {
		companionPod.Labels[k] = v
	}
}

// copyAnnotations copies annotations from source pod to companion pod based on policy
func (r *CompanionPodTemplateReconciler) copyAnnotations(sourcePod, companionPod *corev1.Pod, policy *monitoringv1.AnnotationCopyPolicy) {
	if policy.CopyAll {
		// Copy all annotations except excluded ones
		for k, v := range sourcePod.Annotations {
			if !r.isExcluded(k, policy.Exclude) {
				companionPod.Annotations[k] = v
			}
		}
	} else {
		// Copy only included annotations
		for _, key := range policy.Include {
			if value, exists := sourcePod.Annotations[key]; exists {
				companionPod.Annotations[key] = value
			}
		}
	}

	// Add additional annotations
	for k, v := range policy.AdditionalAnnotations {
		companionPod.Annotations[k] = v
	}
}

// isExcluded checks if a key is in the exclude list
func (r *CompanionPodTemplateReconciler) isExcluded(key string, excludeList []string) bool {
	for _, excluded := range excludeList {
		if key == excluded {
			return true
		}
	}
	return false
}

// updateConditions updates the status conditions
func (r *CompanionPodTemplateReconciler) updateConditions(status *monitoringv1.CompanionPodTemplateStatus, matchedPods, companionPods int32) {
	now := metav1.NewTime(time.Now())

	// Ready condition
	readyCondition := metav1.Condition{
		Type:               "Ready",
		Status:             metav1.ConditionTrue,
		LastTransitionTime: now,
		Reason:             "ReconciliationSuccessful",
		Message:            fmt.Sprintf("Successfully managing %d companion pods for %d matched source pods", companionPods, matchedPods),
	}

	// Update or add the condition
	r.setCondition(status, readyCondition)
}

// setCondition sets a condition in the status
func (r *CompanionPodTemplateReconciler) setCondition(status *monitoringv1.CompanionPodTemplateStatus, condition metav1.Condition) {
	for i, existingCondition := range status.Conditions {
		if existingCondition.Type == condition.Type {
			status.Conditions[i] = condition
			return
		}
	}
	status.Conditions = append(status.Conditions, condition)
}

// SetupWithManager sets up the controller with the Manager.
func (r *CompanionPodTemplateReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&monitoringv1.CompanionPodTemplate{}).
		Owns(&corev1.Pod{}).
		Complete(r)
}
