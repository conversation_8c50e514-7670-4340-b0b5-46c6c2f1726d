package config

import (
	"os"
	"strings"
)

// Config holds the configuration for the monitoring operator
type Config struct {
	// CouchbasePodSelector is the label selector to identify Couchbase pods
	CouchbasePodSelector string

	// NginxImage is the NGINX container image to use
	NginxImage string

	// CustomLabels are additional labels to add to NGINX pods
	CustomLabels map[string]string

	// Namespace is the namespace to watch for Couchbase pods (empty means all namespaces)
	Namespace string

	// NginxPodPrefix is the prefix for NGINX pod names
	NginxPodPrefix string

	// ExcludedLabels are labels that should not be copied from Couchbase pods
	ExcludedLabels []string

	// ExcludedAnnotations are annotations that should not be copied from Couchbase pods
	ExcludedAnnotations []string
}

// NewConfig creates a new configuration from environment variables
func NewConfig() *Config {
	config := &Config{
		CouchbasePodSelector: getEnvOrDefault("COUCHBASE_POD_SELECTOR", "app=couchbase"),
		NginxImage:           getEnvOrDefault("NGINX_IMAGE", "nginx:1.25-alpine"),
		Namespace:            getEnvOrDefault("WATCH_NAMESPACE", ""),
		NginxPodPrefix:       getEnvOrDefault("NGINX_POD_PREFIX", "nginx-companion"),
		CustomLabels:         parseLabels(getEnvOrDefault("CUSTOM_LABELS", "app.kubernetes.io/component=nginx-companion,app.kubernetes.io/managed-by=couchbase-monitoring-operator")),
		ExcludedLabels:       parseStringSlice(getEnvOrDefault("EXCLUDED_LABELS", "controller-revision-hash,statefulset.kubernetes.io/pod-name")),
		ExcludedAnnotations:  parseStringSlice(getEnvOrDefault("EXCLUDED_ANNOTATIONS", "kubectl.kubernetes.io/last-applied-configuration")),
	}

	return config
}

// getEnvOrDefault returns the environment variable value or a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// parseLabels parses a comma-separated string of key=value pairs into a map
func parseLabels(labelsStr string) map[string]string {
	labels := make(map[string]string)
	if labelsStr == "" {
		return labels
	}

	pairs := strings.Split(labelsStr, ",")
	for _, pair := range pairs {
		kv := strings.SplitN(strings.TrimSpace(pair), "=", 2)
		if len(kv) == 2 {
			labels[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
		}
	}

	return labels
}

// parseStringSlice parses a comma-separated string into a slice
func parseStringSlice(str string) []string {
	if str == "" {
		return []string{}
	}

	items := strings.Split(str, ",")
	result := make([]string, 0, len(items))
	for _, item := range items {
		if trimmed := strings.TrimSpace(item); trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return result
}

// ShouldExcludeLabel checks if a label should be excluded from copying
func (c *Config) ShouldExcludeLabel(key string) bool {
	for _, excluded := range c.ExcludedLabels {
		if key == excluded {
			return true
		}
	}
	return false
}

// ShouldExcludeAnnotation checks if an annotation should be excluded from copying
func (c *Config) ShouldExcludeAnnotation(key string) bool {
	for _, excluded := range c.ExcludedAnnotations {
		if key == excluded {
			return true
		}
	}
	return false
}
