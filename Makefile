# Image URL to use all building/pushing image targets
IMG ?= couchbase/monitoring-operator:latest

# Get the currently used golang install path (in GOPATH/bin, unless GOBIN is set)
ifeq (,$(shell go env GOBIN))
GOBIN=$(shell go env GOPATH)/bin
else
GOBIN=$(shell go env GOBIN)
endif

# Setting SHELL to bash allows bash commands to be executed by recipes.
SHELL = /usr/bin/env bash -o pipefail
.SHELLFLAGS = -ec

.PHONY: all
all: build

##@ General

# The help target prints out all targets with their descriptions organized
# beneath their categories. The categories are represented by '##@' and the
# target descriptions by '##'. The awk commands is responsible for reading the
# entire set of makefiles included in this invocation, looking for lines of the
# file as xyz: ## something, and then pretty-format the target and help. Then,
# if there's a line with ##@ something, that gets pretty-printed as a category.
# More info on the usage of ANSI control characters for terminal formatting:
# https://en.wikipedia.org/wiki/ANSI_escape_code#SGR_parameters
# More info on the awk command:
# http://linuxcommand.org/lc3_adv_awk.php

.PHONY: help
help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Development

.PHONY: fmt
fmt: ## Run go fmt against code.
	go fmt ./...

.PHONY: vet
vet: ## Run go vet against code.
	go vet ./...

.PHONY: test
test: fmt vet ## Run tests.
	go test ./... -coverprofile cover.out

##@ Build

.PHONY: build
build: fmt vet ## Build manager binary.
	go build -o bin/manager cmd/manager/main.go

.PHONY: run
run: fmt vet ## Run a controller from your host.
	go run cmd/manager/main.go

.PHONY: docker-build
docker-build: ## Build docker image with the manager.
	docker build -t ${IMG} .

.PHONY: docker-push
docker-push: ## Push docker image with the manager.
	docker push ${IMG}

##@ Deployment

.PHONY: install
install: ## Install CRDs into the K8s cluster specified in ~/.kube/config.
	kubectl apply -f deploy/manifests/crd.yaml

.PHONY: uninstall
uninstall: ## Uninstall CRDs from the K8s cluster specified in ~/.kube/config.
	kubectl delete -f deploy/manifests/crd.yaml

.PHONY: deploy
deploy: ## Deploy controller to the K8s cluster specified in ~/.kube/config.
	kubectl apply -f deploy/manifests/

.PHONY: undeploy
undeploy: ## Undeploy controller from the K8s cluster specified in ~/.kube/config.
	kubectl delete -f deploy/manifests/

##@ Examples

.PHONY: deploy-examples
deploy-examples: ## Deploy example Couchbase StatefulSet and CompanionPodTemplate
	kubectl apply -f examples/couchbase-statefulset.yaml
	kubectl apply -f examples/nginx-companion.yaml

.PHONY: clean-examples
clean-examples: ## Clean up example resources
	kubectl delete -f examples/nginx-companion.yaml --ignore-not-found=true
	kubectl delete -f examples/couchbase-statefulset.yaml --ignore-not-found=true

##@ Utilities

.PHONY: logs
logs: ## Show operator logs
	kubectl logs -n couchbase-monitoring-system deployment/couchbase-monitoring-operator-controller-manager -f

.PHONY: status
status: ## Show status of operator and templates
	@echo "=== Operator Status ==="
	kubectl get pods -n couchbase-monitoring-system
	@echo ""
	@echo "=== CompanionPodTemplates ==="
	kubectl get companionpodtemplates -A
	@echo ""
	@echo "=== Companion Pods ==="
	kubectl get pods -l "app.kubernetes.io/managed-by=couchbase-monitoring-operator" -A
