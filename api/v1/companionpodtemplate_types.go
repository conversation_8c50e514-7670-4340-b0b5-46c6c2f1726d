package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// CompanionPodTemplateSpec defines the desired state of CompanionPodTemplate
type CompanionPodTemplateSpec struct {
	// Selector defines which pods should get companion pods
	// +kubebuilder:validation:Required
	Selector metav1.LabelSelector `json:"selector"`

	// Template defines the pod template for companion pods
	// +kubebuilder:validation:Required
	Template corev1.PodTemplateSpec `json:"template"`

	// LabelCopyPolicy defines how labels should be copied from source pods
	// +kubebuilder:default={copyAll: true}
	LabelCopyPolicy LabelCopyPolicy `json:"labelCopyPolicy,omitempty"`

	// AnnotationCopyPolicy defines how annotations should be copied from source pods
	// +kubebuilder:default={copyAll: true}
	AnnotationCopyPolicy AnnotationCopyPolicy `json:"annotationCopyPolicy,omitempty"`

	// NamingStrategy defines how companion pod names are generated
	// +kubebuilder:default={strategy: "prefix", prefix: "companion"}
	NamingStrategy NamingStrategy `json:"namingStrategy,omitempty"`

	// Lifecycle defines the lifecycle behavior of companion pods
	// +kubebuilder:default={deleteWithSource: true}
	Lifecycle LifecyclePolicy `json:"lifecycle,omitempty"`
}

// LabelCopyPolicy defines how labels are copied from source pods
type LabelCopyPolicy struct {
	// CopyAll indicates whether to copy all labels (default: true)
	// +kubebuilder:default=true
	CopyAll bool `json:"copyAll,omitempty"`

	// Include is a list of label keys to include (only used if CopyAll is false)
	Include []string `json:"include,omitempty"`

	// Exclude is a list of label keys to exclude
	Exclude []string `json:"exclude,omitempty"`

	// AdditionalLabels are extra labels to add to companion pods
	AdditionalLabels map[string]string `json:"additionalLabels,omitempty"`
}

// AnnotationCopyPolicy defines how annotations are copied from source pods
type AnnotationCopyPolicy struct {
	// CopyAll indicates whether to copy all annotations (default: true)
	// +kubebuilder:default=true
	CopyAll bool `json:"copyAll,omitempty"`

	// Include is a list of annotation keys to include (only used if CopyAll is false)
	Include []string `json:"include,omitempty"`

	// Exclude is a list of annotation keys to exclude
	Exclude []string `json:"exclude,omitempty"`

	// AdditionalAnnotations are extra annotations to add to companion pods
	AdditionalAnnotations map[string]string `json:"additionalAnnotations,omitempty"`
}

// NamingStrategy defines how companion pod names are generated
type NamingStrategy struct {
	// Strategy defines the naming strategy ("prefix", "suffix", "template")
	// +kubebuilder:validation:Enum=prefix;suffix;template
	// +kubebuilder:default="prefix"
	Strategy string `json:"strategy,omitempty"`

	// Prefix is used when strategy is "prefix" (default: "companion")
	// +kubebuilder:default="companion"
	Prefix string `json:"prefix,omitempty"`

	// Suffix is used when strategy is "suffix"
	Suffix string `json:"suffix,omitempty"`

	// Template is used when strategy is "template" (e.g., "nginx-{source-name}")
	Template string `json:"template,omitempty"`
}

// LifecyclePolicy defines lifecycle behavior of companion pods
type LifecyclePolicy struct {
	// DeleteWithSource indicates whether companion pods should be deleted when source pod is deleted
	// +kubebuilder:default=true
	DeleteWithSource bool `json:"deleteWithSource,omitempty"`

	// CreateWhenReady indicates whether to wait for source pod to be ready before creating companion
	// +kubebuilder:default=false
	CreateWhenReady bool `json:"createWhenReady,omitempty"`
}

// CompanionPodTemplateStatus defines the observed state of CompanionPodTemplate
type CompanionPodTemplateStatus struct {
	// Conditions represent the latest available observations of the template's state
	Conditions []metav1.Condition `json:"conditions,omitempty"`

	// MatchedPods is the number of source pods that match the selector
	MatchedPods int32 `json:"matchedPods,omitempty"`

	// CompanionPods is the number of companion pods created
	CompanionPods int32 `json:"companionPods,omitempty"`

	// LastReconcileTime is the last time the template was reconciled
	LastReconcileTime *metav1.Time `json:"lastReconcileTime,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:resource:scope=Namespaced
//+kubebuilder:printcolumn:name="Matched Pods",type=integer,JSONPath=`.status.matchedPods`
//+kubebuilder:printcolumn:name="Companion Pods",type=integer,JSONPath=`.status.companionPods`
//+kubebuilder:printcolumn:name="Age",type=date,JSONPath=`.metadata.creationTimestamp`

// CompanionPodTemplate is the Schema for the companionpodtemplates API
type CompanionPodTemplate struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   CompanionPodTemplateSpec   `json:"spec,omitempty"`
	Status CompanionPodTemplateStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// CompanionPodTemplateList contains a list of CompanionPodTemplate
type CompanionPodTemplateList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []CompanionPodTemplate `json:"items"`
}
