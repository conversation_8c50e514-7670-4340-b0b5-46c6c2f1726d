# Couchbase Monitoring Operator

A Kubernetes operator that automatically creates companion pods for Couchbase StatefulSet pods. This operator provides a flexible, CRD-based approach to define companion pod templates that can be used to create monitoring, sidecar, or proxy pods alongside your Couchbase pods.

## Features

- **Flexible Pod Templates**: Define any type of companion pod using Kubernetes PodTemplateSpec
- **Label & Annotation Copying**: Automatically copy labels and annotations from source pods with configurable policies
- **Multiple Naming Strategies**: Support for prefix, suffix, and template-based naming
- **Lifecycle Management**: Automatic cleanup when source pods are deleted
- **Kubernetes Native**: Uses CRDs and follows Kubernetes best practices
- **Multi-Namespace Support**: Can watch pods across multiple namespaces

## Quick Start

### 1. Deploy the Operator

```bash
# Apply CRDs
kubectl apply -f deploy/manifests/crd.yaml

# Apply RBAC
kubectl apply -f deploy/manifests/rbac.yaml

# Deploy the operator
kubectl apply -f deploy/manifests/deployment.yaml
```

### 2. Create a CompanionPodTemplate

Here's a simple example that creates NGINX companion pods for Couchbase pods:

```yaml
apiVersion: monitoring.couchbase.com/v1
kind: CompanionPodTemplate
metadata:
  name: nginx-companion
  namespace: default
spec:
  # Select Couchbase pods
  selector:
    matchLabels:
      app: couchbase
  
  # Define the companion pod template
  template:
    metadata:
      labels:
        app.kubernetes.io/component: nginx-companion
        app.kubernetes.io/managed-by: couchbase-monitoring-operator
    spec:
      containers:
      - name: nginx
        image: nginx:1.25-alpine
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
  
  # Copy all labels except excluded ones
  labelCopyPolicy:
    copyAll: true
    exclude:
    - "controller-revision-hash"
    - "statefulset.kubernetes.io/pod-name"
    additionalLabels:
      companion-type: "nginx"
  
  # Copy all annotations except excluded ones
  annotationCopyPolicy:
    copyAll: true
    exclude:
    - "kubectl.kubernetes.io/last-applied-configuration"
  
  # Use prefix naming strategy
  namingStrategy:
    strategy: "prefix"
    prefix: "nginx-companion"
  
  # Lifecycle configuration
  lifecycle:
    deleteWithSource: true
    createWhenReady: true
```

### 3. Apply the Template

```bash
kubectl apply -f examples/nginx-companion.yaml
```

The operator will automatically create companion NGINX pods for each running Couchbase pod that matches the selector.

## Advanced Examples

### Example 1: Monitoring Sidecar with Prometheus

```yaml
apiVersion: monitoring.couchbase.com/v1
kind: CompanionPodTemplate
metadata:
  name: prometheus-exporter
  namespace: couchbase
spec:
  selector:
    matchLabels:
      app: couchbase
  template:
    metadata:
      labels:
        app.kubernetes.io/component: prometheus-exporter
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9091"
    spec:
      containers:
      - name: couchbase-exporter
        image: couchbase/exporter:latest
        ports:
        - containerPort: 9091
          name: metrics
        env:
        - name: COUCHBASE_URL
          value: "http://localhost:8091"
        - name: COUCHBASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: couchbase-credentials
              key: username
        - name: COUCHBASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: couchbase-credentials
              key: password
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
  namingStrategy:
    strategy: "suffix"
    suffix: "exporter"
```

### Example 2: Log Aggregation with Fluent Bit

```yaml
apiVersion: monitoring.couchbase.com/v1
kind: CompanionPodTemplate
metadata:
  name: log-aggregator
  namespace: couchbase
spec:
  selector:
    matchLabels:
      app: couchbase
  template:
    spec:
      containers:
      - name: fluent-bit
        image: fluent/fluent-bit:2.1
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: config
          mountPath: /fluent-bit/etc
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: config
        configMap:
          name: fluent-bit-config
  namingStrategy:
    strategy: "template"
    template: "logs-{source-name}"
  labelCopyPolicy:
    copyAll: false
    include:
    - "app"
    - "version"
    additionalLabels:
      component: "log-aggregator"
```

## Configuration Options

### Selector

The `selector` field uses standard Kubernetes label selectors to identify which pods should get companion pods.

### Template

The `template` field is a standard Kubernetes `PodTemplateSpec` that defines the companion pod specification.

### Label Copy Policy

- `copyAll`: Copy all labels (default: true)
- `include`: List of label keys to include (only used if copyAll is false)
- `exclude`: List of label keys to exclude
- `additionalLabels`: Extra labels to add

### Annotation Copy Policy

- `copyAll`: Copy all annotations (default: true)
- `include`: List of annotation keys to include (only used if copyAll is false)
- `exclude`: List of annotation keys to exclude
- `additionalAnnotations`: Extra annotations to add

### Naming Strategy

- `prefix`: Add prefix to source pod name (default)
- `suffix`: Add suffix to source pod name
- `template`: Use template with `{source-name}` placeholder

### Lifecycle Policy

- `deleteWithSource`: Delete companion when source pod is deleted (default: true)
- `createWhenReady`: Wait for source pod to be ready before creating companion (default: false)

## Monitoring

The operator exposes metrics and provides status information:

```bash
# Check operator status
kubectl get pods -n couchbase-monitoring-system

# Check CompanionPodTemplate status
kubectl get companionpodtemplates

# Get detailed status
kubectl describe companionpodtemplate nginx-companion
```

## Troubleshooting

### Check Operator Logs

```bash
kubectl logs -n couchbase-monitoring-system deployment/couchbase-monitoring-operator-controller-manager
```

### Common Issues

1. **Companion pods not created**: Check if source pods match the selector
2. **Permission errors**: Verify RBAC configuration
3. **Template validation errors**: Check the pod template specification

## Development

### Building

```bash
# Build the operator
go build -o bin/manager cmd/manager/main.go

# Build Docker image
docker build -t couchbase/monitoring-operator:latest .
```

### Testing

```bash
# Run tests
go test ./...

# Run locally (requires kubeconfig)
go run cmd/manager/main.go
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

Apache License 2.0
