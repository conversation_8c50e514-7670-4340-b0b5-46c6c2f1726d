apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.1
  name: companionpodtemplates.monitoring.couchbase.com
spec:
  group: monitoring.couchbase.com
  names:
    kind: CompanionPodTemplate
    listKind: CompanionPodTemplateList
    plural: companionpodtemplates
    singular: companionpodtemplate
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.matchedPods
      name: Matched Pods
      type: integer
    - jsonPath: .status.companionPods
      name: Companion Pods
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: CompanionPodTemplate is the Schema for the companionpodtemplates API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation of an object.'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this object represents.'
            type: string
          metadata:
            type: object
          spec:
            description: CompanionPodTemplateSpec defines the desired state of CompanionPodTemplate
            properties:
              annotationCopyPolicy:
                default:
                  copyAll: true
                description: AnnotationCopyPolicy defines how annotations should be copied from source pods
                properties:
                  additionalAnnotations:
                    additionalProperties:
                      type: string
                    description: AdditionalAnnotations are extra annotations to add to companion pods
                    type: object
                  copyAll:
                    default: true
                    description: CopyAll indicates whether to copy all annotations (default: true)
                    type: boolean
                  exclude:
                    description: Exclude is a list of annotation keys to exclude
                    items:
                      type: string
                    type: array
                  include:
                    description: Include is a list of annotation keys to include (only used if CopyAll is false)
                    items:
                      type: string
                    type: array
                type: object
              labelCopyPolicy:
                default:
                  copyAll: true
                description: LabelCopyPolicy defines how labels should be copied from source pods
                properties:
                  additionalLabels:
                    additionalProperties:
                      type: string
                    description: AdditionalLabels are extra labels to add to companion pods
                    type: object
                  copyAll:
                    default: true
                    description: CopyAll indicates whether to copy all labels (default: true)
                    type: boolean
                  exclude:
                    description: Exclude is a list of label keys to exclude
                    items:
                      type: string
                    type: array
                  include:
                    description: Include is a list of label keys to include (only used if CopyAll is false)
                    items:
                      type: string
                    type: array
                type: object
              lifecycle:
                default:
                  deleteWithSource: true
                description: Lifecycle defines the lifecycle behavior of companion pods
                properties:
                  createWhenReady:
                    default: false
                    description: CreateWhenReady indicates whether to wait for source pod to be ready before creating companion
                    type: boolean
                  deleteWithSource:
                    default: true
                    description: DeleteWithSource indicates whether companion pods should be deleted when source pod is deleted
                    type: boolean
                type: object
              namingStrategy:
                default:
                  prefix: companion
                  strategy: prefix
                description: NamingStrategy defines how companion pod names are generated
                properties:
                  prefix:
                    default: companion
                    description: Prefix is used when strategy is "prefix" (default: "companion")
                    type: string
                  strategy:
                    default: prefix
                    description: Strategy defines the naming strategy ("prefix", "suffix", "template")
                    enum:
                    - prefix
                    - suffix
                    - template
                    type: string
                  suffix:
                    description: Suffix is used when strategy is "suffix"
                    type: string
                  template:
                    description: Template is used when strategy is "template" (e.g., "nginx-{source-name}")
                    type: string
                type: object
              selector:
                description: Selector defines which pods should get companion pods
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                    items:
                      description: A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to a set of values.
                          type: string
                        values:
                          description: values is an array of string values.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              template:
                description: Template defines the pod template for companion pods
                properties:
                  metadata:
                    description: 'Standard object metadata.'
                    type: object
                  spec:
                    description: 'Specification of the desired behavior of the pod.'
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                type: object
            required:
            - selector
            - template
            type: object
          status:
            description: CompanionPodTemplateStatus defines the observed state of CompanionPodTemplate
            properties:
              companionPods:
                description: CompanionPods is the number of companion pods created
                format: int32
                type: integer
              conditions:
                description: Conditions represent the latest available observations of the template's state
                items:
                  description: "Condition contains details for one aspect of the current state of this API Resource."
                  properties:
                    lastTransitionTime:
                      description: lastTransitionTime is the last time the condition transitioned from one status to another.
                      format: date-time
                      type: string
                    message:
                      description: message is a human readable message indicating details about the transition.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: observedGeneration represents the .metadata.generation that the condition was set based upon.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: reason contains a programmatic identifier indicating the reason for the condition's last transition.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              lastReconcileTime:
                description: LastReconcileTime is the last time the template was reconciled
                format: date-time
                type: string
              matchedPods:
                description: MatchedPods is the number of source pods that match the selector
                format: int32
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
