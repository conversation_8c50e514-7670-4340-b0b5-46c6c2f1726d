apiVersion: v1
kind: ServiceAccount
metadata:
  name: couchbase-monitoring-operator
  namespace: couchbase-monitoring-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: couchbase-monitoring-operator-manager-role
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - pods/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - monitoring.couchbase.com
  resources:
  - companionpodtemplates
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - monitoring.couchbase.com
  resources:
  - companionpodtemplates/finalizers
  verbs:
  - update
- apiGroups:
  - monitoring.couchbase.com
  resources:
  - companionpodtemplates/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: couchbase-monitoring-operator-manager-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: couchbase-monitoring-operator-manager-role
subjects:
- kind: ServiceAccount
  name: couchbase-monitoring-operator
  namespace: couchbase-monitoring-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: couchbase-monitoring-operator-metrics-reader
rules:
- nonResourceURLs:
  - /metrics
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: couchbase-monitoring-operator-proxy-role
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: couchbase-monitoring-operator-proxy-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: couchbase-monitoring-operator-proxy-role
subjects:
- kind: ServiceAccount
  name: couchbase-monitoring-operator
  namespace: couchbase-monitoring-system
