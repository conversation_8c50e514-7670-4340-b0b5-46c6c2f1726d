apiVersion: v1
kind: Namespace
metadata:
  name: couchbase-monitoring-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: couchbase-monitoring-operator-controller-manager
  namespace: couchbase-monitoring-system
  labels:
    app.kubernetes.io/name: couchbase-monitoring-operator
    app.kubernetes.io/component: manager
    app.kubernetes.io/part-of: couchbase-monitoring-operator
    app.kubernetes.io/managed-by: kustomize
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: couchbase-monitoring-operator
      app.kubernetes.io/component: manager
  replicas: 1
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        app.kubernetes.io/name: couchbase-monitoring-operator
        app.kubernetes.io/component: manager
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
                - arm64
                - ppc64le
                - s390x
              - key: kubernetes.io/os
                operator: In
                values:
                - linux
      securityContext:
        runAsNonRoot: true
      containers:
      - command:
        - /manager
        args:
        - --leader-elect
        image: couchbase/monitoring-operator:latest
        name: manager
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          limits:
            cpu: 500m
            memory: 128Mi
          requests:
            cpu: 10m
            memory: 64Mi
        env:
        - name: COUCHBASE_POD_SELECTOR
          value: "app=couchbase"
        - name: NGINX_IMAGE
          value: "nginx:1.25-alpine"
        - name: CUSTOM_LABELS
          value: "app.kubernetes.io/component=nginx-companion,app.kubernetes.io/managed-by=couchbase-monitoring-operator"
        - name: EXCLUDED_LABELS
          value: "controller-revision-hash,statefulset.kubernetes.io/pod-name"
        - name: EXCLUDED_ANNOTATIONS
          value: "kubectl.kubernetes.io/last-applied-configuration"
      serviceAccountName: couchbase-monitoring-operator
      terminationGracePeriodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: couchbase-monitoring-operator-controller-manager-metrics-service
  namespace: couchbase-monitoring-system
  labels:
    app.kubernetes.io/name: couchbase-monitoring-operator
    app.kubernetes.io/component: kube-rbac-proxy
    app.kubernetes.io/part-of: couchbase-monitoring-operator
    app.kubernetes.io/managed-by: kustomize
spec:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: https
  selector:
    app.kubernetes.io/name: couchbase-monitoring-operator
    app.kubernetes.io/component: manager
