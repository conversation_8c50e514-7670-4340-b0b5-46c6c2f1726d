# Example Couchbase StatefulSet for testing the operator
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: couchbase-done
  namespace: default
spec:
  serviceName: couchbase-done
  replicas: 3
  selector:
    matchLabels:
      app: couchbase
  template:
    metadata:
      labels:
        app: couchbase
        version: "7.2.0"
        tier: database
    spec:
      containers:
      - name: couchbase
        image: couchbase:7.2.0
        ports:
        - containerPort: 8091
          name: web-admin
        - containerPort: 8092
          name: views
        - containerPort: 8093
          name: query
        - containerPort: 8094
          name: search
        - containerPort: 8095
          name: analytics
        - containerPort: 8096
          name: eventing
        - containerPort: 11210
          name: memcached
        env:
        - name: CLUSTER_NAME
          value: "couchbase-cluster"
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        volumeMounts:
        - name: couchbase-data
          mountPath: /opt/couchbase/var
        livenessProbe:
          httpGet:
            path: /pools
            port: 8091
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /pools
            port: 8091
          initialDelaySeconds: 30
          periodSeconds: 10
  volumeClaimTemplates:
  - metadata:
      name: couchbase-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: couchbase-done
  namespace: default
spec:
  clusterIP: None
  selector:
    app: couchbase
  ports:
  - name: web-admin
    port: 8091
    targetPort: 8091
  - name: views
    port: 8092
    targetPort: 8092
  - name: query
    port: 8093
    targetPort: 8093
  - name: search
    port: 8094
    targetPort: 8094
  - name: analytics
    port: 8095
    targetPort: 8095
  - name: eventing
    port: 8096
    targetPort: 8096
  - name: memcached
    port: 11210
    targetPort: 11210
