apiVersion: monitoring.couchbase.com/v1
kind: CompanionPodTemplate
metadata:
  name: nginx-companion
  namespace: default
spec:
  # Select Couchbase pods with app=couchbase label
  selector:
    matchLabels:
      app: couchbase
  
  # Define the companion pod template
  template:
    metadata:
      labels:
        app.kubernetes.io/component: nginx-companion
        app.kubernetes.io/managed-by: couchbase-monitoring-operator
        companion-type: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.25-alpine
        ports:
        - containerPort: 80
          name: http
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        # Basic nginx configuration
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-companion-config
          optional: true
      restartPolicy: Always
  
  # Copy all labels except Kubernetes internal ones
  labelCopyPolicy:
    copyAll: true
    exclude:
    - "controller-revision-hash"
    - "statefulset.kubernetes.io/pod-name"
    - "pod-template-hash"
    additionalLabels:
      companion-type: "nginx"
      monitoring.couchbase.com/managed: "true"
  
  # Copy all annotations except kubectl ones
  annotationCopyPolicy:
    copyAll: true
    exclude:
    - "kubectl.kubernetes.io/last-applied-configuration"
    - "kubernetes.io/psp"
    additionalAnnotations:
      monitoring.couchbase.com/companion: "nginx"
  
  # Use prefix naming strategy
  namingStrategy:
    strategy: "prefix"
    prefix: "nginx-companion"
  
  # Lifecycle configuration
  lifecycle:
    deleteWithSource: true
    createWhenReady: true
---
# Optional: ConfigMap for nginx configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-companion-config
  namespace: default
data:
  default.conf: |
    server {
        listen 80;
        server_name localhost;
        
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
        }
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
